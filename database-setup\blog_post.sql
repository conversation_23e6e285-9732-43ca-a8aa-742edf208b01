create table public.blog_posts (
  id text not null,
  title character varying(200) not null,
  slug character varying(250) not null,
  content text not null,
  excerpt character varying(500) null,
  "coverImage" character varying(500) null,
  locale character varying(10) not null,
  category character varying(50) not null,
  tags text[] null,
  status public.PostStatus not null default 'DRAFT'::"PostStatus",
  "publishedAt" timestamp without time zone null,
  "viewCount" integer not null default 0,
  "readingTime" integer not null default 0,
  "seoTitle" character varying(60) null,
  "seoDescription" character varying(160) null,
  keywords text[] null,
  metadata jsonb null,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  "updatedAt" timestamp without time zone not null,
  "scheduledAt" timestamp without time zone null,
  featured boolean null default false,
  "likeCount" integer null default 0,
  "shareCount" integer null default 0,
  "commentCount" integer null default 0,
  constraint blog_posts_pkey primary key (id)
) TABLESPACE pg_default;

create unique INDEX IF not exists blog_posts_slug_key on public.blog_posts using btree (slug) TABLESPACE pg_default;

create index IF not exists blog_posts_locale_category_idx on public.blog_posts using btree (locale, category) TABLESPACE pg_default;

create index IF not exists "blog_posts_status_publishedAt_idx" on public.blog_posts using btree (status, "publishedAt") TABLESPACE pg_default;

create index IF not exists "blog_posts_locale_status_publishedAt_idx" on public.blog_posts using btree (locale, status, "publishedAt") TABLESPACE pg_default;

create index IF not exists "blog_posts_featured_status_publishedAt_idx" on public.blog_posts using btree (featured, status, "publishedAt") TABLESPACE pg_default;