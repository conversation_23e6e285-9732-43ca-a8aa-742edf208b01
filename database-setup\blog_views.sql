create table public.blog_views (
  id text not null,
  "postId" text not null,
  "userId" text null,
  "ipAddress" text null,
  "userAgent" text null,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  constraint blog_views_pkey primary key (id),
  constraint blog_views_postId_fkey foreign KEY ("postId") references blog_posts (id) on update CASCADE on delete CASCADE,
  constraint blog_views_userId_fkey foreign KEY ("userId") references users (id) on update CASCADE on delete set null
) TABLESPACE pg_default;