'use client';

import React, { useState, useEffect } from 'react';
import { Heart, Bookmark, MessageCircle, Eye, Share2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ArticleInteractionsProps {
  postId: string;
  initialLikes?: number;
  initialViews?: number;
  initialComments?: number;
  initialShares?: number;
  userId?: string; // 当前登录用户ID
  className?: string;
  variant?: 'floating' | 'inline' | 'compact';
  showCounts?: boolean;
  onShare?: (platform: string) => void;
}

interface InteractionStats {
  views: number;
  likes: number;
  shares: number;
  comments: number;
}

interface UserState {
  isLiked: boolean;
  isBookmarked: boolean;
}

export function ArticleInteractions({
  postId,
  initialLikes = 0,
  initialViews = 0,
  initialComments = 0,
  initialShares = 0,
  userId,
  className,
  variant = 'inline',
  showCounts = true,
  onShare,
}: ArticleInteractionsProps) {
  const [stats, setStats] = useState<InteractionStats>({
    views: initialViews,
    likes: initialLikes,
    shares: initialShares,
    comments: initialComments,
  });
  const [userState, setUserState] = useState<UserState>({
    isLiked: false,
    isBookmarked: false,
  });
  const [isVisible, setIsVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    // 获取用户交互状态和最新统计数据
    fetchInteractionData();

    // 浮动模式的可见性控制
    if (variant === 'floating') {
      const handleScroll = () => {
        const scrollTop = window.pageYOffset;
        setIsVisible(scrollTop > 300);
      };

      window.addEventListener('scroll', handleScroll, { passive: true });
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [postId, variant, userId]);

  const fetchInteractionData = async (): Promise<void> => {
    try {
      const params = new URLSearchParams();
      if (userId) params.set('userId', userId);
      
      const response = await fetch(`/api/blog/${postId}/interactions?${params}`);
      const data = await response.json();
      
      if (data.success) {
        setStats(data.data.stats);
        setUserState(data.data.userState);
      }
    } catch (error) {
      console.error('Failed to fetch interaction data:', error);
      // 降级到localStorage检查（兼容性）
      const likedPosts = JSON.parse(localStorage.getItem('likedPosts') || '[]');
      const bookmarkedPosts = JSON.parse(localStorage.getItem('bookmarkedPosts') || '[]');
      
      setUserState({
        isLiked: likedPosts.includes(postId),
        isBookmarked: bookmarkedPosts.includes(postId),
      });
    }
  };

  const handleLike = async () => {
    if (isLoading) return;
    
    setIsLoading(true);
    const originalState = { ...userState };
    const originalStats = { ...stats };
    
    try {
      // 乐观更新UI
      const newIsLiked = !userState.isLiked;
      setUserState(prev => ({ ...prev, isLiked: newIsLiked }));
      setStats(prev => ({ 
        ...prev, 
        likes: newIsLiked ? prev.likes + 1 : prev.likes - 1 
      }));

      // 调用API
      const response = await fetch(`/api/blog/${postId}/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: newIsLiked ? 'like' : 'unlike',
          userId,
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to update like');
      }

      // 更新本地存储（降级兼容）
      const likedPosts = JSON.parse(localStorage.getItem('likedPosts') || '[]');
      if (newIsLiked) {
        if (!likedPosts.includes(postId)) {
          likedPosts.push(postId);
        }
      } else {
        const index = likedPosts.indexOf(postId);
        if (index > -1) {
          likedPosts.splice(index, 1);
        }
      }
      localStorage.setItem('likedPosts', JSON.stringify(likedPosts));

    } catch (error) {
      console.error('Failed to update like:', error);
      // 回滚状态
      setUserState(originalState);
      setStats(originalStats);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBookmark = async () => {
    if (isLoading) return;
    
    if (!userId) {
      alert('请先登录后再收藏文章');
      return;
    }
    
    setIsLoading(true);
    const originalState = { ...userState };
    
    try {
      // 乐观更新UI
      const newIsBookmarked = !userState.isBookmarked;
      setUserState(prev => ({ ...prev, isBookmarked: newIsBookmarked }));

      // 调用API
      const response = await fetch(`/api/blog/${postId}/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: newIsBookmarked ? 'bookmark' : 'unbookmark',
          userId,
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(data.error || 'Failed to update bookmark');
      }

      // 更新本地存储（降级兼容）
      const bookmarkedPosts = JSON.parse(localStorage.getItem('bookmarkedPosts') || '[]');
      if (newIsBookmarked) {
        if (!bookmarkedPosts.includes(postId)) {
          bookmarkedPosts.push(postId);
        }
      } else {
        const index = bookmarkedPosts.indexOf(postId);
        if (index > -1) {
          bookmarkedPosts.splice(index, 1);
        }
      }
      localStorage.setItem('bookmarkedPosts', JSON.stringify(bookmarkedPosts));

    } catch (error) {
      console.error('Failed to update bookmark:', error);
      // 回滚状态
      setUserState(originalState);
    } finally {
      setIsLoading(false);
    }
  };

  const handleShare = async (platform: string = 'general') => {
    try {
      // 乐观更新分享计数
      setStats(prev => ({ ...prev, shares: prev.shares + 1 }));

      // 调用API记录分享
      const response = await fetch(`/api/blog/${postId}/interactions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'share',
          userId,
          platform,
        }),
      });

      const data = await response.json();
      
      if (!data.success) {
        console.warn('Failed to record share:', data.error);
        // 不回滚UI，分享记录失败不影响用户体验
      }

      // 触发外部分享回调
      if (onShare) {
        onShare(platform);
      }

    } catch (error) {
      console.error('Failed to record share:', error);
      // 分享记录失败不影响用户体验，不回滚UI
    }
  };

  const interactions = [
    {
      icon: Heart,
      count: stats.likes,
      isActive: userState.isLiked,
      onClick: handleLike,
      label: 'Like',
      activeColor: 'text-red-500',
      activeBg: 'bg-red-50 dark:bg-red-900/20',
      disabled: isLoading,
    },
    {
      icon: Bookmark,
      count: null,
      isActive: userState.isBookmarked,
      onClick: handleBookmark,
      label: 'Bookmark',
      activeColor: 'text-yellow-500',
      activeBg: 'bg-yellow-50 dark:bg-yellow-900/20',
      disabled: isLoading,
    },
    {
      icon: MessageCircle,
      count: stats.comments,
      isActive: false,
      onClick: () => {
        // 滚动到评论区域
        const commentsSection = document.getElementById('comments');
        if (commentsSection) {
          commentsSection.scrollIntoView({ behavior: 'smooth' });
        }
      },
      label: 'Comments',
      activeColor: 'text-blue-500',
      activeBg: 'bg-blue-50 dark:bg-blue-900/20',
      disabled: false,
    },
    {
      icon: Share2,
      count: stats.shares,
      isActive: false,
      onClick: () => handleShare('general'),
      label: 'Share',
      activeColor: 'text-green-500',
      activeBg: 'bg-green-50 dark:bg-green-900/20',
      disabled: false,
    },
  ];

  if (variant === 'floating') {
    return (
      <div
        className={cn(
          'transition-all duration-300',
          isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-4',
          className
        )}
      >
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 p-3">
          <div className="flex flex-col gap-3">
            {/* 浏览量显示 */}
            <div className="flex flex-col items-center gap-1">
              <Eye className="w-4 h-4 text-gray-400" />
              <span className="text-xs text-gray-500 dark:text-gray-400">
                {stats.views}
              </span>
            </div>
            
            <div className="border-t border-gray-200 dark:border-gray-700 pt-3">
              <div className="flex flex-col gap-3">
                {interactions.map((interaction, index) => (
                  <button
                    key={index}
                    onClick={interaction.onClick}
                    disabled={interaction.disabled}
                    className={cn(
                      'flex flex-col items-center gap-1 p-2 rounded-lg transition-all duration-200',
                      interaction.disabled && 'opacity-50 cursor-not-allowed',
                      interaction.isActive
                        ? cn(interaction.activeBg, interaction.activeColor)
                        : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
                    )}
                    title={interaction.label}
                  >
                    <interaction.icon className="w-4 h-4" />
                    {interaction.count !== null && showCounts && (
                      <span className="text-xs">
                        {interaction.count}
                      </span>
                    )}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (variant === 'compact') {
    return (
      <div className={cn('flex items-center gap-2', className)}>
        {interactions.map((interaction, index) => (
          <button
            key={index}
            onClick={interaction.onClick}
            disabled={interaction.disabled}
            className={cn(
              'flex items-center gap-1 px-2 py-1 rounded-lg transition-all duration-200 text-sm',
              interaction.disabled && 'opacity-50 cursor-not-allowed',
              interaction.isActive
                ? cn(interaction.activeBg, interaction.activeColor)
                : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700'
            )}
          >
            <interaction.icon className="w-4 h-4" />
            {interaction.count !== null && showCounts && (
              <span>{interaction.count}</span>
            )}
          </button>
        ))}
      </div>
    );
  }

  // Default inline variant
  return (
    <div className={cn('flex items-center justify-between py-4 border-y border-mystical-200 dark:border-dark-700', className)}>
      <div className="flex items-center gap-4">
        {interactions.map((interaction, index) => (
          <button
            key={index}
            onClick={interaction.onClick}
            disabled={interaction.disabled}
            className={cn(
              'flex items-center gap-2 px-3 py-2 rounded-lg transition-all duration-200',
              interaction.disabled && 'opacity-50 cursor-not-allowed',
              interaction.isActive
                ? cn(interaction.activeBg, interaction.activeColor)
                : 'text-mystical-600 dark:text-mystical-400 hover:bg-mystical-100 dark:hover:bg-dark-700'
            )}
          >
            <interaction.icon className="w-4 h-4" />
            <span className="text-sm font-medium">{interaction.label}</span>
            {interaction.count !== null && showCounts && (
              <span className="text-sm">({interaction.count})</span>
            )}
          </button>
        ))}
      </div>
      
      {/* 浏览量显示 */}
      <div className="flex items-center gap-1 text-mystical-500 dark:text-mystical-400">
        <Eye className="w-4 h-4" />
        <span className="text-sm">{stats.views} views</span>
      </div>
    </div>
  );
}
