/**
 * 博文浏览次数API - /api/blog/[id]/view
 * 处理博文浏览次数增加
 */

import { NextRequest, NextResponse } from 'next/server';
import { DatabaseService } from '@/lib/database-service';

interface RouteParams {
  params: {
    id: string;
  };
}

// 获取客户端IP地址
function getClientIP(request: NextRequest): string {
  const forwarded = request.headers.get('x-forwarded-for');
  const realIP = request.headers.get('x-real-ip');
  
  if (forwarded) {
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  return 'unknown';
}

// 记录浏览次数
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { id: postId } = params;
    const body = await request.json();
    const { userId } = body;
    
    const ipAddress = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';

    // 记录浏览并增加计数
    await DatabaseService.recordView(postId, userId, ipAddress, userAgent);

    return NextResponse.json({
      success: true,
      message: 'View recorded successfully'
    });

  } catch (error: any) {
    console.error('Error recording view:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to record view',
        message: error.message,
      },
      { status: 500 }
    );
  }
}
