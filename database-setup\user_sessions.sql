create table public.user_sessions (
  id text not null,
  "userId" text not null,
  "sessionToken" text not null,
  "refreshToken" text not null,
  "expiresAt" timestamp without time zone not null,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  "updatedAt" timestamp without time zone not null,
  constraint user_sessions_pkey primary key (id),
  constraint user_sessions_userId_fkey foreign KEY ("userId") references users (id) on update CASCADE on delete CASCADE
) TABLESPACE pg_default;

create unique INDEX IF not exists "user_sessions_sessionToken_key" on public.user_sessions using btree ("sessionToken") TABLESPACE pg_default;

create unique INDEX IF not exists "user_sessions_refreshToken_key" on public.user_sessions using btree ("refreshToken") TABLESPACE pg_default;