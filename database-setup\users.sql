create table public.users (
  id text not null,
  email text not null,
  username text null,
  avatar text null,
  locale text not null default 'en'::text,
  theme text not null default 'light'::text,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  "updatedAt" timestamp without time zone not null,
  constraint users_pkey primary key (id)
) TABLESPACE pg_default;

create unique INDEX IF not exists users_email_key on public.users using btree (email) TABLESPACE pg_default;

create unique INDEX IF not exists users_username_key on public.users using btree (username) TABLESPACE pg_default;