create table public.user_verifications (
  id text not null,
  "userId" text null,
  email text null,
  token text not null,
  type public.VerificationType not null,
  "expiresAt" timestamp without time zone not null,
  used boolean not null default false,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  constraint user_verifications_pkey primary key (id),
  constraint user_verifications_userId_fkey foreign KEY ("userId") references users (id) on update CASCADE on delete CASCADE
) TABLESPACE pg_default;

create unique INDEX IF not exists user_verifications_token_key on public.user_verifications using btree (token) TABLESPACE pg_default;