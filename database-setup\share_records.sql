create table public.share_records (
  id text not null,
  "postId" text not null,
  "userId" text null,
  platform character varying(50) not null,
  "ipAddress" text null,
  "userAgent" text null,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  constraint share_records_pkey primary key (id),
  constraint share_records_postId_fkey foreign KEY ("postId") references blog_posts (id) on update CASCAD<PERSON> on delete CASCADE,
  constraint share_records_userId_fkey foreign KEY ("userId") references users (id) on update CASCADE on delete set null
) TABLESPACE pg_default;

create index IF not exists "share_records_postId_idx" on public.share_records using btree ("postId") TABLESPACE pg_default;