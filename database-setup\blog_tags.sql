create table public.blog_tags (
  id text not null default (gen_random_uuid ())::text,
  name character varying(50) not null,
  slug character varying(60) not null,
  description character varying(200) null,
  color character varying(7) null,
  locale character varying(10) not null,
  "postCount" integer null default 0,
  "createdAt" timestamp without time zone null default CURRENT_TIMESTAMP,
  "updatedAt" timestamp without time zone null default CURRENT_TIMESTAMP,
  constraint blog_tags_pkey primary key (id),
  constraint blog_tags_slug_key unique (slug)
) TABLESPACE pg_default;

create index IF not exists blog_tags_locale_idx on public.blog_tags using btree (locale) TABLESPACE pg_default;

create index IF not exists blog_tags_slug_idx on public.blog_tags using btree (slug) TABLESPACE pg_default;

create trigger update_blog_tags_updated_at BEFORE
update on blog_tags for EACH row
execute FUNCTION update_updated_at_column ();