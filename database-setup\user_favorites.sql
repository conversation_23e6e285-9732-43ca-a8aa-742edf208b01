create table public.user_favorites (
  id text not null,
  "userId" text not null,
  "postId" text not null,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  constraint user_favorites_pkey primary key (id),
  constraint user_favorites_postId_fkey foreign KEY ("postId") references blog_posts (id) on update CASCADE on delete CASCADE,
  constraint user_favorites_userId_fkey foreign KEY ("userId") references users (id) on update CASCADE on delete CASCADE
) TABLESPACE pg_default;

create unique INDEX IF not exists "user_favorites_userId_postId_key" on public.user_favorites using btree ("userId", "postId") TABLESPACE pg_default;