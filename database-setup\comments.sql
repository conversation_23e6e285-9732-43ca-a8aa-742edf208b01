create table public.comments (
  id text not null,
  content text not null,
  "userId" text null,
  "postId" text not null,
  "parentId" text null,
  "isApproved" boolean not null default false,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  "updatedAt" timestamp without time zone not null,
  constraint comments_pkey primary key (id),
  constraint comments_parentId_fkey foreign KEY ("parentId") references comments (id) on update CASCADE on delete set null,
  constraint comments_postId_fkey foreign KEY ("postId") references blog_posts (id) on update CASCAD<PERSON> on delete CASCADE,
  constraint comments_userId_fkey foreign KEY ("userId") references users (id) on update CASCADE on delete set null
) TABLESPACE pg_default;