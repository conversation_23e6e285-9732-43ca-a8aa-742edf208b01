create table public.blog_categories (
  id text not null default (gen_random_uuid ())::text,
  name character varying(100) not null,
  slug character varying(120) not null,
  description character varying(500) null,
  color character varying(7) null,
  icon character varying(50) null,
  image character varying(500) null,
  locale character varying(10) not null,
  "postCount" integer null default 0,
  "seoTitle" character varying(60) null,
  "seoDescription" character varying(160) null,
  "createdAt" timestamp without time zone null default CURRENT_TIMESTAMP,
  "updatedAt" timestamp without time zone null default CURRENT_TIMESTAMP,
  constraint blog_categories_pkey primary key (id),
  constraint blog_categories_slug_key unique (slug)
) TABLESPACE pg_default;

create index IF not exists blog_categories_locale_idx on public.blog_categories using btree (locale) TABLESPACE pg_default;

create index IF not exists blog_categories_slug_idx on public.blog_categories using btree (slug) TABLESPACE pg_default;

create trigger update_blog_categories_updated_at BEFORE
update on blog_categories for EACH row
execute FUNCTION update_updated_at_column ();