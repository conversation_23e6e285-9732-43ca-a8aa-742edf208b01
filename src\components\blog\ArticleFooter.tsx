'use client';

import React from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { BlogPost } from '@/types';
import { Tag, Share2, Heart, Bookmark, Twitter, Facebook, Linkedin, Copy } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';

interface ArticleFooterProps {
  post: BlogPost;
  relatedPosts: BlogPost[];
  className?: string;
}

/**
 * 文章底部组件 - 基于01-frontend-design-rules.md的博客设计规范
 */
export function ArticleFooter({ post, relatedPosts, className }: ArticleFooterProps) {
  const t = useTranslations('blog');

  const handleShare = async (platform: string) => {
    const url = window.location.href;
    const title = post.title;
    const text = post.excerpt || '';

    switch (platform) {
      case 'twitter':
        window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(url)}&text=${encodeURIComponent(title)}`, '_blank');
        break;
      case 'facebook':
        window.open(`https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'linkedin':
        window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
        break;
      case 'copy':
        try {
          await navigator.clipboard.writeText(url);
          // 这里可以添加一个toast通知
        } catch (err) {
          console.error('Failed to copy URL:', err);
        }
        break;
    }
  };

  return (
    <footer className={cn('mt-16 space-y-8', className)}>
      {/* 分隔线 */}
      <div className="flex items-center justify-center my-12">
        <div className="text-gray-400 dark:text-gray-600 text-2xl tracking-widest">✦ ✦ ✦</div>
      </div>

      {/* 标签区域 */}
      {post.tags && post.tags.length > 0 && (
        <div className="space-y-4">
          <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
            <Tag className="w-5 h-5" />
            {t('tags')}
          </h3>
          <div className="flex flex-wrap gap-2">
            {post.tags.map((tag) => (
              <Link
                key={tag}
                href={`/${post.locale}/blog?tag=${encodeURIComponent(tag)}`}
                className="inline-flex items-center px-3 py-1.5 text-sm font-medium
                  bg-gray-100 dark:bg-gray-800 
                  text-gray-700 dark:text-gray-300
                  border border-gray-200 dark:border-gray-700
                  rounded-full transition-all duration-200
                  hover:bg-gray-200 dark:hover:bg-gray-700
                  hover:border-gray-300 dark:hover:border-gray-600
                  hover:transform hover:scale-105"
              >
                #{tag}
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 社交分享区域 - 简化版本 */}
      <div className="space-y-4">
        <h3 className="flex items-center gap-2 text-lg font-semibold text-gray-900 dark:text-gray-100">
          <Share2 className="w-5 h-5" />
          {t('shareArticle')}
        </h3>
        <div className="flex gap-3">
          <button
            onClick={() => handleShare('twitter')}
            className="flex items-center justify-center w-10 h-10 rounded-lg
              bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 transition-all duration-200
              hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-500 hover:scale-105"
            title="Share on Twitter"
          >
            <Twitter className="w-4 h-4" />
          </button>
          <button
            onClick={() => handleShare('facebook')}
            className="flex items-center justify-center w-10 h-10 rounded-lg
              bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 transition-all duration-200
              hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-600 hover:scale-105"
            title="Share on Facebook"
          >
            <Facebook className="w-4 h-4" />
          </button>
          <button
            onClick={() => handleShare('linkedin')}
            className="flex items-center justify-center w-10 h-10 rounded-lg
              bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 transition-all duration-200
              hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 hover:scale-105"
            title="Share on LinkedIn"
          >
            <Linkedin className="w-4 h-4" />
          </button>
          <button
            onClick={() => handleShare('copy')}
            className="flex items-center justify-center w-10 h-10 rounded-lg
              bg-gray-100 dark:bg-gray-800 text-gray-600 dark:text-gray-400 transition-all duration-200
              hover:bg-green-50 dark:hover:bg-green-900/20 hover:text-green-600 hover:scale-105"
            title="Copy link"
          >
            <Copy className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* 作者简介卡片 */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-2xl p-6 border border-gray-200 dark:border-gray-700">
        <div className="flex items-start gap-4">
          {/* 作者头像 */}
          <div className="flex-shrink-0">
            <div className="w-16 h-16 rounded-full bg-gradient-to-br from-gray-400 to-gray-600 flex items-center justify-center text-white font-bold text-xl">
              A
            </div>
          </div>
          
          {/* 作者信息 */}
          <div className="flex-1">
            <h4 className="text-xl font-bold text-gray-900 dark:text-gray-100 mb-2">
              {t('author')}
            </h4>
            <p className="text-gray-600 dark:text-gray-300 leading-relaxed mb-4">
              {t('authorBio')}
            </p>
            <button className="inline-flex items-center px-4 py-2 text-sm font-medium
              text-gray-600 dark:text-gray-400
              border border-gray-300 dark:border-gray-600
              rounded-full transition-all duration-200
              hover:bg-gray-100 dark:hover:bg-gray-700
              hover:border-gray-400 dark:hover:border-gray-500">
              {t('followAuthor')}
            </button>
          </div>
        </div>
      </div>

      {/* 相关文章推荐 */}
      {relatedPosts && relatedPosts.length > 0 && (
        <div className="space-y-6">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100 text-center">
            {t('relatedPosts')}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {relatedPosts.slice(0, 3).map((relatedPost) => (
              <Link
                key={relatedPost.id}
                href={`/${relatedPost.locale}/blog/${relatedPost.category}/${relatedPost.slug}`}
                className="group block bg-white dark:bg-gray-800 rounded-xl overflow-hidden
                  border border-gray-200 dark:border-gray-700
                  transition-all duration-300
                  hover:transform hover:scale-105 hover:shadow-lg
                  hover:border-gray-300 dark:hover:border-gray-600"
              >
                {/* 文章图片 */}
                {relatedPost.coverImage && (
                  <div className="aspect-[16/9] overflow-hidden">
                    <Image
                      src={relatedPost.coverImage}
                      alt={relatedPost.title}
                      width={400}
                      height={225}
                      className="w-full h-full object-cover transition-transform duration-300
                        group-hover:scale-110"
                    />
                  </div>
                )}
                
                {/* 文章内容 */}
                <div className="p-4">
                  <div className="text-xs font-semibold text-gray-600 dark:text-gray-400 uppercase tracking-wide mb-2">
                    {relatedPost.category}
                  </div>
                  <h4 className="text-lg font-bold text-gray-900 dark:text-gray-100 mb-2 line-clamp-2
                    group-hover:text-gray-700 dark:group-hover:text-gray-300 transition-colors">
                    {relatedPost.title}
                  </h4>
                  {relatedPost.excerpt && (
                    <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-3 mb-3">
                      {relatedPost.excerpt}
                    </p>
                  )}
                  <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                    <span>{relatedPost.readingTime} min read</span>
                    <span>{relatedPost.viewCount} views</span>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* 移除重复的文章互动统计，这些信息已经在右侧浮动栏显示 */}
    </footer>
  );
}
