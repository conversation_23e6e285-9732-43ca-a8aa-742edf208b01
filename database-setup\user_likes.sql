create table public.user_likes (
  id text not null,
  "userId" text null,
  "postId" text not null,
  "ipAddress" text null,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  constraint user_likes_pkey primary key (id),
  constraint user_likes_postId_fkey foreign <PERSON>E<PERSON> ("postId") references blog_posts (id) on update CASCADE on delete CASCADE,
  constraint user_likes_userId_fkey foreign KEY ("userId") references users (id) on update CASCAD<PERSON> on delete CASCADE
) TABLESPACE pg_default;

create unique INDEX IF not exists "user_likes_userId_postId_key" on public.user_likes using btree ("userId", "postId") TABLESPACE pg_default;

create unique INDEX IF not exists "user_likes_ipAddress_postId_key" on public.user_likes using btree ("ipAddress", "postId") TABLESPACE pg_default;

create index IF not exists "user_likes_postId_userId_idx" on public.user_likes using btree ("postId", "userId") TABLESPACE pg_default;

create index IF not exists "user_likes_postId_ipAddress_idx" on public.user_likes using btree ("postId", "ipAddress") TABLESPACE pg_default;