'use client';

import React, { useState, useEffect } from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FocusModeProps {
  className?: string;
  position?: 'fixed' | 'sticky';
}

export function FocusMode({ className, position = 'fixed' }: FocusModeProps) {
  const [isFocusMode, setIsFocusMode] = useState(false);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const scrollTop = window.pageYOffset;
      setIsVisible(scrollTop > 200); // 滚动200px后显示
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    if (isFocusMode) {
      document.body.classList.add('focus-mode');
      
      // 应用专注阅读样式 - 隐藏所有侧边栏和导航
      const style = document.createElement('style');
      style.id = 'focus-mode-styles';
      style.textContent = `
        /* 隐藏顶部导航栏 */
        .focus-mode header,
        .focus-mode nav {
          display: none !important;
        }
        
        /* 隐藏所有浮动侧边栏 */
        .focus-mode .fixed {
          display: none !important;
        }
        
        /* 保持专注模式控制按钮可见 */
        .focus-mode .focus-mode-controls {
          display: block !important;
        }
        
        /* 保持文章原有布局，不修改位置和宽度 */
      `;
      document.head.appendChild(style);
    } else {
      document.body.classList.remove('focus-mode');
      const style = document.getElementById('focus-mode-styles');
      if (style) {
        style.remove();
      }
    }

    return () => {
      document.body.classList.remove('focus-mode');
      const style = document.getElementById('focus-mode-styles');
      if (style) {
        style.remove();
      }
    };
  }, [isFocusMode]);

  const containerClasses = cn(
    'focus-mode-controls transition-all duration-300 z-50',
    {
      'fixed right-6 bottom-6': position === 'fixed',
      'sticky top-6': position === 'sticky',
      'opacity-100 translate-y-0': isVisible || position === 'sticky',
      'opacity-0 translate-y-4 pointer-events-none': !isVisible && position === 'fixed',
    },
    className
  );

  return (
    <div className={containerClasses}>
      {/* 专注阅读模式切换按钮 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
        <button
          onClick={() => setIsFocusMode(!isFocusMode)}
          className={cn(
            'flex items-center justify-center w-12 h-12 rounded-lg transition-all duration-200',
            isFocusMode
              ? 'bg-blue-500 text-white shadow-lg'
              : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-600'
          )}
          title={isFocusMode ? '退出专注阅读' : '进入专注阅读'}
        >
          {isFocusMode ? <EyeOff className="w-6 h-6" /> : <Eye className="w-6 h-6" />}
        </button>
      </div>
    </div>
  );
}
