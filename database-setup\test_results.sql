create table public.test_results (
  id text not null,
  "userId" text null,
  "testType" public.TestType not null,
  answers jsonb not null,
  result jsonb not null,
  "shareToken" text null,
  "isPublic" boolean not null default false,
  "createdAt" timestamp without time zone not null default CURRENT_TIMESTAMP,
  constraint test_results_pkey primary key (id),
  constraint test_results_userId_fkey foreign KEY ("userId") references users (id) on update CASCADE on delete set null
) TABLESPACE pg_default;

create unique INDEX IF not exists "test_results_shareToken_key" on public.test_results using btree ("shareToken") TABLESPACE pg_default;